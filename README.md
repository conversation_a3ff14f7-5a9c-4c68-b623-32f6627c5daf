# Strix Auth API

Проектът е RESTful API, реализиран с Laravel и MySQL/MariaDB, с автентикация и ауторизация, базирана на роли. Подготвен е за бъдеща локализация на интерфейса.

## Основни функционалности
- Управление на превозни средства (Vehicles) с тип, регистрация, VIN, километраж, прикачени файлове, технически прегледи и винетки
- Групи от потребители с ролеви достъп: `admin`, `power user`, `regular user`, `guest`
- Автентикация чрез Laravel Sanctum (API tokens)
- Само администратор може да създава нови потребители и да вижда всички потребители
- Първият потребител (админ) се създава автоматично при инсталация
- Подготовка за локализация (middleware и структура)

## Инсталация
1. Клонирайте репото:
   ```bash
   git clone <repo-url>
   cd strix-auth-api
   ```
2. Инсталирайте зависимостите:
   ```bash
   composer install
   ```
3. Създайте .env файл и конфигурирайте достъпа до MySQL/MariaDB:
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=strix_auth
   DB_USERNAME=your_user
   DB_PASSWORD=your_password
   ```
4. Мигрирайте базата и seed-нете първия админ:
   ```bash
   php artisan migrate
   php artisan db:seed --class=AdminUserSeeder
   ```
   Ако добавите нови модели или миграции (например за Vehicles или Groups), изпълнете отново:
   ```bash
   php artisan migrate
   ```
5. Стартирайте сървъра:
   ```bash
   php artisan serve
   ```

## Първи администратор
- Email: `<EMAIL>`
- Парола: `admin1234`

## Роли и достъп до превозни средства
- `admin`: вижда всички превозни средства, управлява групи
- `power user`: вижда свои превозни средства и тези на групите, в които е мениджър
- `regular user`: вижда само свои превозни средства
- `guest`: вижда само превозни средства, за които има изрични права (по подразбиране - няма)
- Групите имат owner (admin), мениджъри (power user) и членове (regular, guest)

## Vehicles & Groups
- Достъп до меню Vehicles за всички удостоверени потребители
- CRUD операции за превозни средства според роля и асоциации
- Групи с owner (admin), мениджъри (power user), членове (regular user, guest)
- Миграции: `php artisan migrate` за създаване на нужните таблици

## API маршрути
| Метод | Маршрут        | Описание                       |
|-------|----------------|--------------------------------|
| POST  | /api/login     | Вход (email, password)         |
| POST  | /api/logout    | Изход (изисква токен)          |
| GET   | /api/users     | Списък на потребителите (admin) |
| POST  | /api/users     | Създаване на потребител (admin) |

### Пример: Login
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin1234"}'
```

### Пример: Създаване на потребител
```bash
curl -X POST http://localhost:8000/api/users \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"testpass123","role":"regular user"}'
```

## Локализация
- Проектът е подготвен за добавяне на middleware и файлове за превод.
- Може да се разшири с Laravel localization пакети и ресурси.

## Лиценз
MIT
