<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\VehicleController;
use App\Http\Controllers\MaintenanceController;
use App\Http\Controllers\ExportController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\UserGroupController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/api-docs', function () {
    return view('api-docs');
})->name('api.docs');

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Vehicle routes - accessible to all authenticated users
    Route::resource('vehicles', VehicleController::class);

    // Maintenance routes - accessible to all authenticated users
    Route::resource('maintenances', MaintenanceController::class);

    // Export routes
    Route::get('export/vehicles', [ExportController::class, 'vehiclesCsv'])->name('export.vehicles');
});

// Admin routes - only for admin users
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    Route::resource('users', UserController::class);
    Route::resource('groups', UserGroupController::class);

    // Additional group member management routes
    Route::post('groups/{group}/members', [UserGroupController::class, 'addMember'])->name('groups.members.add');
    Route::delete('groups/{group}/members/{user}', [UserGroupController::class, 'removeMember'])->name('groups.members.remove');

    // Admin export routes
    Route::get('export/users', [ExportController::class, 'usersCsv'])->name('export.users');
    Route::get('export/groups', [ExportController::class, 'groupsCsv'])->name('export.groups');
});

require __DIR__.'/auth.php';
