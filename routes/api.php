<?php

use App\Http\Controllers\Api\VehicleApiController;
use App\Http\Controllers\Api\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Auth routes (без middleware)
Route::post('login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('logout', [AuthController::class, 'logout']);
    Route::get('me', [AuthController::class, 'me']);

    Route::get('/user', function (Request $request) {
        return $request->user();
    });
});

// API Routes за превозни средства
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('vehicles', VehicleApiController::class);
    
    // Допълнителни API endpoints
    Route::get('vehicle-types', function () {
        return response()->json([
            'success' => true,
            'data' => \App\Models\Vehicle::getVehicleTypes()
        ]);
    });
    
    Route::get('vehicles/{vehicle}/maintenances', function (\App\Models\Vehicle $vehicle) {
        return response()->json([
            'success' => true,
            'data' => $vehicle->maintenances()->orderBy('date', 'desc')->get()
        ]);
    });
});
