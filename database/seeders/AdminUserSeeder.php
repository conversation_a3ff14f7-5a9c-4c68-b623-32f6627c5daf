<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (\App\Models\User::count() > 0) {
            return;
        }

        // Създаване на ролите
        $roles = ['admin', 'power user', 'regular user', 'guest'];
        foreach ($roles as $role) {
            \Spatie\Permission\Models\Role::firstOrCreate(['name' => $role]);
        }

        // Създаване на първи администратор
        $admin = \App\Models\User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('admin1234'),
        ]);
        $admin->assignRole('admin');
    }
}
