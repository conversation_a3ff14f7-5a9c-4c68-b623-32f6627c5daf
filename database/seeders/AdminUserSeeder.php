<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Създаване на admin потребител
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('admin123456'),
                'email_verified_at' => now(),
            ]
        );

        // Присвояване на admin роля
        $admin->assignRole('admin');

        echo "Admin потребител създаден:\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123456\n";
    }
}
