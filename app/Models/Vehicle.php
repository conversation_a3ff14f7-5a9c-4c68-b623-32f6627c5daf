<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Vehicle extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'registration_date',
        'registration_number',
        'vin',
        'initial_mileage',
        'user_id',
        'group_id',
    ];

    public function attachments(): Has<PERSON>any
    {
        return $this->hasMany(VehicleAttachment::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function technicalInspections(): Has<PERSON><PERSON>
    {
        return $this->hasMany(TechnicalInspection::class);
    }

    public function vignettes(): HasMany
    {
        return $this->hasMany(Vignette::class);
    }
}
