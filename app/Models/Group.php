<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Group extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'owner_id',
    ];

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function managers()
    {
        return $this->belongsToMany(User::class, 'group_managers');
    }

    public function members()
    {
        return $this->belongsToMany(User::class, 'group_members');
    }

    public function vehicles()
    {
        return $this->hasMany(Vehicle::class);
    }
}
