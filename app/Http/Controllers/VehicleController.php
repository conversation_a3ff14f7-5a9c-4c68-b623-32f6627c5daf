<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use App\Models\TechnicalInspection;
use App\Models\Vignette;
use App\Models\VehicleAttachment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class VehicleController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $vehicles = Vehicle::with(['attachments', 'technicalInspections', 'vignettes']);

        if ($user->hasRole('admin')) {
            // Admin вижда всички
            $vehicles = $vehicles->get();
        } elseif ($user->hasRole('power user')) {
            // Power user: свои, групови, с дадени права (тук само свои и групови)
            $groupIds = $user->groupsAsManager()->pluck('groups.id')->toArray();
            $vehicles = $vehicles->where(function($q) use ($user, $groupIds) {
                $q->where('user_id', $user->id)
                  ->orWhereIn('group_id', $groupIds);
            })->get();
        } elseif ($user->hasRole('regular user')) {
            // Regular user: свои и с дадени права (тук само свои)
            $vehicles = $vehicles->where('user_id', $user->id)->get();
        } elseif ($user->hasRole('guest')) {
            // Guest: само с дадени права (тук празно)
            $vehicles = collect();
        } else {
            $vehicles = collect();
        }
        return view('vehicles.index', compact('vehicles'));
    }

    public function create()
    {
        return view('vehicles.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'type' => 'required|string',
            'registration_date' => 'required|date',
            'registration_number' => 'required|string|unique:vehicles',
            'vin' => 'required|string|unique:vehicles',
            'initial_mileage' => 'required|integer',
            'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf',
        ]);

        $vehicle = Vehicle::create($validated);

        // Handle attachments
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('vehicle_attachments', 'public');
                VehicleAttachment::create([
                    'vehicle_id' => $vehicle->id,
                    'file_path' => $path,
                ]);
            }
        }

        return redirect()->route('vehicles.index');
    }

    public function show(Vehicle $vehicle)
    {
        $vehicle->load(['attachments', 'technicalInspections', 'vignettes']);
        return view('vehicles.show', compact('vehicle'));
    }

    public function edit(Vehicle $vehicle)
    {
        $vehicle->load(['attachments', 'technicalInspections', 'vignettes']);
        return view('vehicles.edit', compact('vehicle'));
    }

    public function update(Request $request, Vehicle $vehicle)
    {
        $validated = $request->validate([
            'type' => 'required|string',
            'registration_date' => 'required|date',
            'registration_number' => 'required|string|unique:vehicles,registration_number,' . $vehicle->id,
            'vin' => 'required|string|unique:vehicles,vin,' . $vehicle->id,
            'initial_mileage' => 'required|integer',
            'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf',
        ]);
        $vehicle->update($validated);

        // Handle new attachments
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('vehicle_attachments', 'public');
                VehicleAttachment::create([
                    'vehicle_id' => $vehicle->id,
                    'file_path' => $path,
                ]);
            }
        }

        return redirect()->route('vehicles.show', $vehicle);
    }

    public function destroy(Vehicle $vehicle)
    {
        $vehicle->delete();
        return redirect()->route('vehicles.index');
    }
}
