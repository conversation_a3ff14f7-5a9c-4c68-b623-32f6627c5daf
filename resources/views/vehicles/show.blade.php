@extends('layouts.app')

@section('content')
<div class="container">
    <h1>Превозно средство: {{ $vehicle->registration_number }}</h1>
    <a href="{{ route('vehicles.edit', $vehicle) }}" class="btn btn-warning">Редакция</a>
    <a href="{{ route('vehicles.index') }}" class="btn btn-secondary">Назад</a>
    <hr>
    <dl class="row">
        <dt class="col-sm-3">Вид</dt><dd class="col-sm-9">{{ $vehicle->type }}</dd>
        <dt class="col-sm-3">Дата на регистрация</dt><dd class="col-sm-9">{{ $vehicle->registration_date }}</dd>
        <dt class="col-sm-3">VIN</dt><dd class="col-sm-9">{{ $vehicle->vin }}</dd>
        <dt class="col-sm-3">Първоначален километраж</dt><dd class="col-sm-9">{{ $vehicle->initial_mileage }}</dd>
    </dl>
    <h3>Прикачени файлове</h3>
    <ul>
        @foreach($vehicle->attachments as $attachment)
            <li><a href="{{ asset('storage/' . $attachment->file_path) }}" target="_blank">{{ basename($attachment->file_path) }}</a></li>
        @endforeach
    </ul>
    <h3>Технически прегледи</h3>
    <ul>
        @foreach($vehicle->technicalInspections as $inspection)
            <li>{{ $inspection->inspection_date }} @if($inspection->notes) - {{ $inspection->notes }} @endif</li>
        @endforeach
    </ul>
    <h3>Винетки</h3>
    <ul>
        @foreach($vehicle->vignettes as $vignette)
            <li>Закупена на: {{ $vignette->purchase_date }}, валидна до: {{ $vignette->valid_until }} @if($vignette->document) - <a href="{{ asset('storage/' . $vignette->document) }}" target="_blank">документ</a> @endif</li>
        @endforeach
    </ul>
</div>
@endsection
