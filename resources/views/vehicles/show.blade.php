@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">{{ $vehicle->full_name }}</h1>
            <div class="space-x-2">
                <a href="{{ route('vehicles.edit', $vehicle) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Редактирай
                </a>
                <a href="{{ route('vehicles.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Назад
                </a>
            </div>
        </div>

        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-gray-50 border-b">
                <h2 class="text-xl font-semibold text-gray-800">Основна информация</h2>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Вид превозно средство</label>
                        <p class="text-lg text-gray-900">{{ $vehicle->type }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Регистрационен номер</label>
                        <p class="text-lg text-gray-900 font-semibold">{{ $vehicle->registration_number }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">VIN номер</label>
                        <p class="text-lg text-gray-900 font-mono">{{ $vehicle->vin }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Дата на регистрация</label>
                        <p class="text-lg text-gray-900">{{ $vehicle->registration_date->format('d.m.Y') }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Първоначален километраж</label>
                        <p class="text-lg text-gray-900">{{ number_format($vehicle->initial_mileage) }} км</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Текущ километраж</label>
                        <p class="text-lg text-gray-900">{{ number_format($vehicle->current_mileage) }} км</p>
                    </div>

                    @if($vehicle->make)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Марка</label>
                        <p class="text-lg text-gray-900">{{ $vehicle->make }}</p>
                    </div>
                    @endif

                    @if($vehicle->model)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Модел</label>
                        <p class="text-lg text-gray-900">{{ $vehicle->model }}</p>
                    </div>
                    @endif

                    @if($vehicle->year)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Година на производство</label>
                        <p class="text-lg text-gray-900">{{ $vehicle->year }}</p>
                    </div>
                    @endif

                    @if($vehicle->color)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Цвят</label>
                        <p class="text-lg text-gray-900">{{ $vehicle->color }}</p>
                    </div>
                    @endif

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Статус</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $vehicle->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $vehicle->is_active ? 'Активно' : 'Неактивно' }}
                        </span>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Създадено на</label>
                        <p class="text-lg text-gray-900">{{ $vehicle->created_at->format('d.m.Y H:i') }}</p>
                    </div>
                </div>

                @if($vehicle->notes)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Забележки</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ $vehicle->notes }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <div class="mt-6 flex justify-end space-x-4">
            <form action="{{ route('vehicles.destroy', $vehicle) }}" method="POST" class="inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" onclick="return confirm('Сигурни ли сте, че искате да изтриете това превозно средство?')">
                    Изтрий превозно средство
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
