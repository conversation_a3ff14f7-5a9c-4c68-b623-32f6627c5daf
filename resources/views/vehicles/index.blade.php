@extends('layouts.app')

@section('content')
<div class="container">
    <h1>Превозни средства</h1>
    <a href="{{ route('vehicles.create') }}" class="btn btn-primary mb-3">Добави превозно средство</a>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Вид</th>
                <th>Рег. номер</th>
                <th>VIN</th>
                <th>Дата на регистрация</th>
                <th>Първоначален километраж</th>
                <th>Действия</th>
            </tr>
        </thead>
        <tbody>
            @forelse($vehicles as $vehicle)
                <tr>
                    <td>{{ $vehicle->id }}</td>
                    <td>{{ $vehicle->type }}</td>
                    <td>{{ $vehicle->registration_number }}</td>
                    <td>{{ $vehicle->vin }}</td>
                    <td>{{ $vehicle->registration_date }}</td>
                    <td>{{ $vehicle->initial_mileage }}</td>
                    <td>
                        <a href="{{ route('vehicles.show', $vehicle) }}" class="btn btn-info btn-sm">Преглед</a>
                        <a href="{{ route('vehicles.edit', $vehicle) }}" class="btn btn-warning btn-sm">Редакция</a>
                        <form action="{{ route('vehicles.destroy', $vehicle) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm">Изтрий</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="text-center">Няма налични превозни средства.</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>
@endsection
