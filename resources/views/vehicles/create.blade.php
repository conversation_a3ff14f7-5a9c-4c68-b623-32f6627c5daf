@extends('layouts.app')

@section('content')
<div class="container">
    <h1>Добави превозно средство</h1>
    <form action="{{ route('vehicles.store') }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="mb-3">
            <label for="type" class="form-label">Вид</label>
            <input type="text" name="type" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="registration_date" class="form-label">Дата на регистрация</label>
            <input type="date" name="registration_date" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="registration_number" class="form-label">Рег. номер</label>
            <input type="text" name="registration_number" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="vin" class="form-label">VIN</label>
            <input type="text" name="vin" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="initial_mileage" class="form-label">Първоначален километраж</label>
            <input type="number" name="initial_mileage" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="attachments" class="form-label">Прикачени файлове (снимки/документи)</label>
            <input type="file" name="attachments[]" class="form-control" multiple>
        </div>
        <button type="submit" class="btn btn-success">Запази</button>
        <a href="{{ route('vehicles.index') }}" class="btn btn-secondary">Отказ</a>
    </form>
</div>
@endsection
