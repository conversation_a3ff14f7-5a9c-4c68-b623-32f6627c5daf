@extends('layouts.app')

@section('content')
<div class="container">
    <h1>Редакция на превозно средство</h1>
    <form action="{{ route('vehicles.update', $vehicle) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        <div class="mb-3">
            <label for="type" class="form-label">Вид</label>
            <input type="text" name="type" class="form-control" value="{{ $vehicle->type }}" required>
        </div>
        <div class="mb-3">
            <label for="registration_date" class="form-label">Дата на регистрация</label>
            <input type="date" name="registration_date" class="form-control" value="{{ $vehicle->registration_date }}" required>
        </div>
        <div class="mb-3">
            <label for="registration_number" class="form-label">Рег. номер</label>
            <input type="text" name="registration_number" class="form-control" value="{{ $vehicle->registration_number }}" required>
        </div>
        <div class="mb-3">
            <label for="vin" class="form-label">VIN</label>
            <input type="text" name="vin" class="form-control" value="{{ $vehicle->vin }}" required>
        </div>
        <div class="mb-3">
            <label for="initial_mileage" class="form-label">Първоначален километраж</label>
            <input type="number" name="initial_mileage" class="form-control" value="{{ $vehicle->initial_mileage }}" required>
        </div>
        <div class="mb-3">
            <label for="attachments" class="form-label">Добави файлове</label>
            <input type="file" name="attachments[]" class="form-control" multiple>
        </div>
        <button type="submit" class="btn btn-success">Запази</button>
        <a href="{{ route('vehicles.index') }}" class="btn btn-secondary">Отказ</a>
    </form>
    <hr>
    <h3>Съществуващи файлове</h3>
    <ul>
        @foreach($vehicle->attachments as $attachment)
            <li><a href="{{ asset('storage/' . $attachment->file_path) }}" target="_blank">{{ basename($attachment->file_path) }}</a></li>
        @endforeach
    </ul>
</div>
@endsection
