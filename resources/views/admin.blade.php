<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Admin Panel') }}
        </h2>
    </x-slot>

    <div class="max-w-7xl mx-auto py-8 px-4">
        <div class="mb-6">
            <p>Welcome, {{ Auth::user()->name }}! This is the admin panel.</p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- User Management Card -->
            <a href="{{ route('admin.users.index') }}" class="group bg-white rounded shadow p-6 flex flex-col justify-between cursor-pointer transition hover:shadow-lg hover:ring-2 hover:ring-blue-400 focus:outline-none">
                <div>
                    <h3 class="text-lg font-semibold mb-2 group-hover:text-blue-700 transition">Управление на потребителите</h3>
                    <p class="mb-4 text-gray-600">До<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, редакция и премахване на потребители, роли и права.</p>
                </div>
                <span class="inline-block px-4 py-2 mt-4 bg-blue-600 text-white rounded group-hover:bg-blue-700 transition w-max">Управление на потребители</span>
            </a>
            <!-- System Settings Card -->
            <a href="{{ route('admin.settings') }}" class="group bg-white rounded shadow p-6 flex flex-col justify-between cursor-pointer transition hover:shadow-lg hover:ring-2 hover:ring-green-400 focus:outline-none">
                <div>
                    <h3 class="text-lg font-semibold mb-2 group-hover:text-green-700 transition">Системни настройки</h3>
                    <p class="mb-4 text-gray-600">Настройки на системата, локализация и други глобални опции.</p>
                </div>
                <span class="inline-block px-4 py-2 mt-4 bg-green-600 text-white rounded group-hover:bg-green-700 transition w-max">Системни настройки</span>
            </a>
        </div>
    </div>
</x-app-layout>
