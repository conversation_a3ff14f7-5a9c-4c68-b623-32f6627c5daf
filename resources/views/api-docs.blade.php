<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StrixVehicle API Документация</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-gray-800 mb-8">StrixVehicle API Документация</h1>
            
            <!-- Authentication -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Аутентикация</h2>
                <p class="text-gray-600 mb-4">API използва Laravel Sanctum за аутентикация с Bearer токени.</p>
                
                <div class="bg-gray-50 rounded p-4 mb-4">
                    <h3 class="font-semibold mb-2">POST /api/login</h3>
                    <p class="text-sm text-gray-600 mb-2">Вход в системата и получаване на API токен</p>
                    <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "email": "<EMAIL>",
  "password": "admin123456"
}</code></pre>
                </div>
                
                <div class="bg-gray-50 rounded p-4 mb-4">
                    <h3 class="font-semibold mb-2">POST /api/logout</h3>
                    <p class="text-sm text-gray-600 mb-2">Изход от системата (изисква Bearer токен)</p>
                </div>
                
                <div class="bg-gray-50 rounded p-4">
                    <h3 class="font-semibold mb-2">GET /api/me</h3>
                    <p class="text-sm text-gray-600 mb-2">Информация за текущия потребител (изисква Bearer токен)</p>
                </div>
            </div>

            <!-- Vehicles API -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Превозни средства API</h2>
                
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded p-4">
                        <h3 class="font-semibold mb-2 text-green-600">GET /api/vehicles</h3>
                        <p class="text-sm text-gray-600 mb-2">Списък с всички превозни средства</p>
                        <p class="text-xs text-gray-500 mb-2">Параметри: type, is_active, search</p>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>GET /api/vehicles?type=car&is_active=1&search=CA1234</code></pre>
                    </div>
                    
                    <div class="bg-gray-50 rounded p-4">
                        <h3 class="font-semibold mb-2 text-blue-600">POST /api/vehicles</h3>
                        <p class="text-sm text-gray-600 mb-2">Създаване на ново превозно средство</p>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "type": "car",
  "registration_number": "CA1234AB",
  "vin": "1HGBH41JXMN109186",
  "registration_date": "2024-01-15",
  "initial_mileage": 0,
  "make": "Toyota",
  "model": "Camry",
  "year": 2024,
  "color": "Бял"
}</code></pre>
                    </div>
                    
                    <div class="bg-gray-50 rounded p-4">
                        <h3 class="font-semibold mb-2 text-green-600">GET /api/vehicles/{id}</h3>
                        <p class="text-sm text-gray-600 mb-2">Детайли за конкретно превозно средство</p>
                    </div>
                    
                    <div class="bg-gray-50 rounded p-4">
                        <h3 class="font-semibold mb-2 text-yellow-600">PUT /api/vehicles/{id}</h3>
                        <p class="text-sm text-gray-600 mb-2">Обновяване на превозно средство</p>
                    </div>
                    
                    <div class="bg-gray-50 rounded p-4">
                        <h3 class="font-semibold mb-2 text-red-600">DELETE /api/vehicles/{id}</h3>
                        <p class="text-sm text-gray-600 mb-2">Изтриване на превозно средство</p>
                    </div>
                </div>
            </div>

            <!-- Additional Endpoints -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Допълнителни endpoints</h2>
                
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded p-4">
                        <h3 class="font-semibold mb-2">GET /api/vehicle-types</h3>
                        <p class="text-sm text-gray-600 mb-2">Списък с всички типове превозни средства</p>
                    </div>
                    
                    <div class="bg-gray-50 rounded p-4">
                        <h3 class="font-semibold mb-2">GET /api/vehicles/{id}/maintenances</h3>
                        <p class="text-sm text-gray-600 mb-2">Обслужвания за конкретно превозно средство</p>
                    </div>
                </div>
            </div>

            <!-- Response Format -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Формат на отговорите</h2>
                
                <div class="bg-gray-50 rounded p-4 mb-4">
                    <h3 class="font-semibold mb-2">Успешен отговор</h3>
                    <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": { ... },
  "message": "Операцията е успешна"
}</code></pre>
                </div>
                
                <div class="bg-gray-50 rounded p-4">
                    <h3 class="font-semibold mb-2">Грешка</h3>
                    <pre class="bg-gray-800 text-red-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": false,
  "message": "Описание на грешката",
  "errors": { ... }
}</code></pre>
                </div>
            </div>

            <!-- Headers -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">HTTP Headers</h2>
                
                <div class="space-y-2">
                    <div class="bg-gray-50 rounded p-3">
                        <code class="text-sm">Content-Type: application/json</code>
                    </div>
                    <div class="bg-gray-50 rounded p-3">
                        <code class="text-sm">Accept: application/json</code>
                    </div>
                    <div class="bg-gray-50 rounded p-3">
                        <code class="text-sm">Authorization: Bearer {your-token}</code>
                    </div>
                </div>
            </div>

            <!-- Test Credentials -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-2">Тестови данни</h3>
                <p class="text-blue-700 mb-2">За тестване на API можете да използвате:</p>
                <div class="bg-blue-100 rounded p-3">
                    <p class="text-sm"><strong>Email:</strong> <EMAIL></p>
                    <p class="text-sm"><strong>Password:</strong> admin123456</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
