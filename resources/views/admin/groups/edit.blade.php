@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">Редактиране на група</h1>
            <a href="{{ route('admin.groups.show', $group) }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Назад
            </a>
        </div>

        <div class="bg-white shadow-md rounded-lg p-6">
            <form action="{{ route('admin.groups.update', $group) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Име на групата *</label>
                        <input type="text" name="name" id="name" value="{{ old('name', $group->name) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                        @error('name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Описание</label>
                        <textarea name="description" id="description" rows="4" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('description') border-red-500 @enderror">{{ old('description', $group->description) }}</textarea>
                        @error('description')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="owner_id" class="block text-sm font-medium text-gray-700 mb-2">Собственик (Администратор) *</label>
                        <select name="owner_id" id="owner_id" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('owner_id') border-red-500 @enderror">
                            <option value="">Изберете администратор</option>
                            @foreach($admins as $admin)
                                <option value="{{ $admin->id }}" {{ old('owner_id', $group->owner_id) == $admin->id ? 'selected' : '' }}>
                                    {{ $admin->name }} ({{ $admin->email }})
                                </option>
                            @endforeach
                        </select>
                        @error('owner_id')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Само администратори могат да бъдат собственици на групи</p>
                    </div>

                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="is_active" value="1" {{ old('is_active', $group->is_active) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Активна група</span>
                        </label>
                    </div>
                </div>

                <!-- Current Info -->
                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h3 class="text-sm font-medium text-blue-700 mb-2">Текуща информация:</h3>
                    <div class="text-xs text-blue-600 space-y-1">
                        <p><strong>Създадена на:</strong> {{ $group->created_at->format('d.m.Y H:i') }}</p>
                        <p><strong>Последна промяна:</strong> {{ $group->updated_at->format('d.m.Y H:i') }}</p>
                        <p><strong>Брой членове:</strong> {{ $group->members->count() }}</p>
                        <p><strong>Текущ собственик:</strong> {{ $group->owner->name ?? 'Няма' }}</p>
                    </div>
                </div>

                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{{ route('admin.groups.show', $group) }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Отказ
                    </a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Обнови група
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
