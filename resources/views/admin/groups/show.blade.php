@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">{{ $group->name }}</h1>
            <div class="space-x-2">
                <a href="{{ route('admin.groups.edit', $group) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Редактирай
                </a>
                <a href="{{ route('admin.groups.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Назад
                </a>
            </div>
        </div>

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {{ session('error') }}
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Group Information -->
            <div class="bg-white shadow-md rounded-lg overflow-hidden">
                <div class="px-6 py-4 bg-gray-50 border-b">
                    <h2 class="text-xl font-semibold text-gray-800">Информация за групата</h2>
                </div>
                
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Име</label>
                            <p class="text-lg text-gray-900">{{ $group->name }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Описание</label>
                            <p class="text-gray-900">{{ $group->description ?? 'Няма описание' }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Собственик</label>
                            <p class="text-lg text-gray-900">{{ $group->owner->name ?? 'Няма собственик' }}</p>
                            @if($group->owner)
                                <p class="text-sm text-gray-500">{{ $group->owner->email }}</p>
                            @endif
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Статус</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $group->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $group->is_active ? 'Активна' : 'Неактивна' }}
                            </span>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Създадена на</label>
                            <p class="text-gray-900">{{ $group->created_at->format('d.m.Y H:i') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Member Form -->
            <div class="bg-white shadow-md rounded-lg overflow-hidden">
                <div class="px-6 py-4 bg-gray-50 border-b">
                    <h2 class="text-xl font-semibold text-gray-800">Добави член</h2>
                </div>
                
                <div class="p-6">
                    <form action="{{ route('admin.groups.members.add', $group) }}" method="POST">
                        @csrf
                        
                        <div class="space-y-4">
                            <div>
                                <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">Потребител</label>
                                <select name="user_id" id="user_id" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('user_id') border-red-500 @enderror">
                                    <option value="">Изберете потребител</option>
                                    @foreach($availableUsers as $user)
                                        <option value="{{ $user->id }}">
                                            {{ $user->name }} ({{ $user->email }}) -
                                            @if($user->hasRole('admin'))
                                                Admin
                                            @elseif($user->hasRole('power_user'))
                                                Power User
                                            @elseif($user->hasRole('regular_user'))
                                                Regular User
                                            @elseif($user->hasRole('guest'))
                                                Guest
                                            @else
                                                Няма роля
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                                @error('user_id')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="role_in_group" class="block text-sm font-medium text-gray-700 mb-2">Роля в групата</label>
                                <select name="role_in_group" id="role_in_group" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('role_in_group') border-red-500 @enderror">
                                    <option value="">Изберете роля</option>
                                    <option value="member">Член</option>
                                    <option value="manager">Мениджър</option>
                                </select>
                                @error('role_in_group')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                                <div class="text-xs text-gray-500 mt-1">
                                    <p><strong>Мениджъри:</strong> Power User или Admin роля</p>
                                    <p><strong>Членове:</strong> Guest или по-висока роля</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Добави член
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Members List -->
        <div class="mt-8 bg-white shadow-md rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-gray-50 border-b">
                <h2 class="text-xl font-semibold text-gray-800">Членове на групата ({{ $group->members->count() }})</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full table-auto">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Потребител</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Системна роля</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Роля в групата</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Присъединен на</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($group->members as $member)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $member->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $member->email }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($member->hasRole('admin')) bg-red-100 text-red-800
                                        @elseif($member->hasRole('power_user')) bg-purple-100 text-purple-800
                                        @elseif($member->hasRole('regular_user')) bg-blue-100 text-blue-800
                                        @else bg-gray-100 text-gray-800
                                        @endif">
                                        @if($member->hasRole('admin'))
                                            Admin
                                        @elseif($member->hasRole('power_user'))
                                            Power User
                                        @elseif($member->hasRole('regular_user'))
                                            Regular User
                                        @elseif($member->hasRole('guest'))
                                            Guest
                                        @else
                                            Няма роля
                                        @endif
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $member->pivot->role_in_group === 'manager' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800' }}">
                                        {{ $member->pivot->role_in_group === 'manager' ? 'Мениджър' : 'Член' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ \Carbon\Carbon::parse($member->pivot->joined_at)->format('d.m.Y H:i') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <form action="{{ route('admin.groups.members.remove', [$group, $member]) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Сигурни ли сте?')">
                                            Премахни
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                    Няма членове в тази група.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Delete Group -->
        <div class="mt-6 flex justify-end">
            <form action="{{ route('admin.groups.destroy', $group) }}" method="POST" class="inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" onclick="return confirm('Сигурни ли сте, че искате да изтриете тази група?')">
                    Изтрий група
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
