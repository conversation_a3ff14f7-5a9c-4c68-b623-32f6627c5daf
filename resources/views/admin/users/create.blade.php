<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Създаване на потребител') }}
        </h2>
    </x-slot>

    <div class="container mx-auto py-8">
        <div class="max-w-lg mx-auto bg-white rounded shadow p-6">
            <form action="{{ route('admin.users.store') }}" method="POST">
                @csrf
                <div class="mb-4">
                    <label class="block mb-1 font-semibold">Име</label>
                    <input type="text" name="name" class="w-full border rounded px-3 py-2" required value="{{ old('name') }}">
                </div>
                <div class="mb-4">
                    <label class="block mb-1 font-semibold">Email</label>
                    <input type="email" name="email" class="w-full border rounded px-3 py-2" required value="{{ old('email') }}">
                </div>
                <div class="mb-4">
                    <label class="block mb-1 font-semibold">Парола</label>
                    <input type="password" name="password" class="w-full border rounded px-3 py-2" required>
                </div>
                <div class="mb-4">
                    <label class="block mb-1 font-semibold">Роля</label>
                    <select name="role" class="w-full border rounded px-3 py-2" required>
                        @foreach($roles as $role)
                            <option value="{{ $role }}">{{ $role }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="flex justify-end">
                    <a href="{{ route('admin.users.index') }}" class="px-4 py-2 bg-gray-300 rounded mr-2">Отказ</a>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">Създай</button>
                </div>
            </form>
        </div>
    </div>
</x-app-layout>
