<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Списък на потребителите') }}
        </h2>
    </x-slot>

    <div class="container mx-auto py-8">
        <div class="flex justify-between mb-4">
            <h3 class="text-lg font-semibold">Всички потребители</h3>
            <a href="{{ route('admin.users.create') }}" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">+ Нов потребител</a>
        </div>
        <div class="bg-white rounded shadow p-6 overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr>
                        <th class="px-4 py-2">#</th>
                        <th class="px-4 py-2">Име</th>
                        <th class="px-4 py-2">Email</th>
                        <th class="px-4 py-2">Роля</th>
                        <th class="px-4 py-2">Действия</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($users as $user)
                        <tr>
                            <td class="border px-4 py-2">{{ $user->id }}</td>
                            <td class="border px-4 py-2">{{ $user->name }}</td>
                            <td class="border px-4 py-2">{{ $user->email }}</td>
                            <td class="border px-4 py-2">{{ $user->getRoleNames()->implode(', ') }}</td>
                            <td class="border px-4 py-2">
                                <a href="{{ route('admin.users.edit', $user) }}" class="text-blue-600 hover:underline mr-2">Редакция</a>
                                <form action="{{ route('admin.users.destroy', $user) }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:underline" onclick="return confirm('Сигурни ли сте?')">Изтриване</button>
                                </form>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</x-app-layout>
